package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.entity.domain.v1.member.MemberSession;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缓存类型转换测试
 * 验证Redis反序列化时的类型转换逻辑
 *
 * <AUTHOR>
 */
class CacheTypeConversionTest {

    @Test
    void testJSONObjectToMemberSession() {
        // Given - 创建一个MemberSession对象
        MemberSession originalSession = new MemberSession();
        originalSession.setId(1L);
        originalSession.setMemberId(100L);
        originalSession.setToken("test.jwt.token");
        originalSession.setIsActive(true);
        originalSession.setLoginTime(OffsetDateTime.now());

        // 模拟Redis序列化和反序列化过程
        String jsonString = JSON.toJSONString(originalSession);
        JSONObject jsonObject = JSON.parseObject(jsonString);

        // When - 将JSONObject转换回MemberSession
        MemberSession convertedSession = JSON.parseObject(JSON.toJSONString(jsonObject), MemberSession.class);

        // Then - 验证转换结果
        assertNotNull(convertedSession);
        assertEquals(originalSession.getId(), convertedSession.getId());
        assertEquals(originalSession.getMemberId(), convertedSession.getMemberId());
        assertEquals(originalSession.getToken(), convertedSession.getToken());
        assertEquals(originalSession.getIsActive(), convertedSession.getIsActive());
    }

    @Test
    void testJSONArrayToMemberSessionList() {
        // Given - 创建MemberSession列表
        MemberSession session1 = new MemberSession();
        session1.setId(1L);
        session1.setMemberId(100L);
        session1.setToken("token1");
        session1.setIsActive(true);

        MemberSession session2 = new MemberSession();
        session2.setId(2L);
        session2.setMemberId(100L);
        session2.setToken("token2");
        session2.setIsActive(true);

        List<MemberSession> originalSessions = Arrays.asList(session1, session2);

        // 模拟Redis序列化和反序列化过程
        String jsonString = JSON.toJSONString(originalSessions);
        List<JSONObject> jsonObjects = JSON.parseArray(jsonString, JSONObject.class);

        // When - 将JSONObject列表转换回MemberSession列表
        List<MemberSession> convertedSessions = jsonObjects.stream()
                .map(item -> JSON.parseObject(JSON.toJSONString(item), MemberSession.class))
                .collect(Collectors.toList());

        // Then - 验证转换结果
        assertNotNull(convertedSessions);
        assertEquals(2, convertedSessions.size());

        assertEquals(session1.getId(), convertedSessions.getFirst().getId());
        assertEquals(session1.getMemberId(), convertedSessions.get(0).getMemberId());
        assertEquals(session1.getToken(), convertedSessions.get(0).getToken());

        assertEquals(session2.getId(), convertedSessions.get(1).getId());
        assertEquals(session2.getMemberId(), convertedSessions.get(1).getMemberId());
        assertEquals(session2.getToken(), convertedSessions.get(1).getToken());
    }

    @Test
    void testDirectListConversion() {
        // Given - 创建MemberSession列表
        MemberSession session = new MemberSession();
        session.setId(1L);
        session.setMemberId(100L);
        session.setToken("test.token");
        session.setIsActive(true);

        List<MemberSession> originalSessions = List.of(session);

        // When - 直接使用JSON.parseArray转换
        String jsonString = JSON.toJSONString(originalSessions);
        List<MemberSession> convertedSessions = JSON.parseArray(jsonString, MemberSession.class);

        // Then - 验证转换结果
        assertNotNull(convertedSessions);
        assertEquals(1, convertedSessions.size());
        assertEquals(session.getId(), convertedSessions.getFirst().getId());
        assertEquals(session.getMemberId(), convertedSessions.getFirst().getMemberId());
        assertEquals(session.getToken(), convertedSessions.getFirst().getToken());
        assertEquals(session.getIsActive(), convertedSessions.getFirst().getIsActive());
    }

    @Test
    void testListMutability() {
        // Given - 创建MemberSession列表
        MemberSession session1 = new MemberSession();
        session1.setId(1L);
        session1.setMemberId(100L);
        session1.setToken("token1");
        session1.setLoginTime(OffsetDateTime.now().minusHours(2));

        MemberSession session2 = new MemberSession();
        session2.setId(2L);
        session2.setMemberId(100L);
        session2.setToken("token2");
        session2.setLoginTime(OffsetDateTime.now().minusHours(1));

        List<MemberSession> originalSessions = Arrays.asList(session1, session2);

        // 模拟Redis序列化和反序列化过程
        String jsonString = JSON.toJSONString(originalSessions);
        List<JSONObject> jsonObjects = JSON.parseArray(jsonString, JSONObject.class);

        // When - 转换并测试可变性
        List<MemberSession> convertedSessions = jsonObjects.stream()
                .map(item -> JSON.parseObject(JSON.toJSONString(item), MemberSession.class))
                .collect(Collectors.toList());

        // Then - 验证列表是可变的，可以进行排序
        assertDoesNotThrow(() -> {
            convertedSessions.sort(java.util.Comparator.comparing(MemberSession::getLoginTime));
        });

        // 验证排序结果
        assertEquals(session1.getId(), convertedSessions.get(0).getId()); // 更早的时间排在前面
        assertEquals(session2.getId(), convertedSessions.get(1).getId());
    }
}
