package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.docs.ModelApiAnnotations;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.ParamValidateUtil;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.param.ModelByCodeParam;
import ai.showlab.bff.entity.param.ModelDetailParam;
import ai.showlab.bff.entity.param.ModelListParam;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.bff.service.v1.model.IModelCategoryService;
import ai.showlab.bff.service.v1.model.IModelService;
import ai.showlab.common.core.web.domain.RestResult;
import ai.showlab.common.core.web.page.PageResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 模型接口 (面向外部普通会员)
 * 提供AI模型相关的查询功能，包括模型列表、详情、分类等
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/model")
@Tag(name = "模型接口", description = "提供AI模型的查询功能，包括模型列表、详情、分类等，面向普通会员使用。")
public class ModelController extends BaseController {

    @Autowired
    private IModelService modelService;

    @Autowired
    private IModelCategoryService modelCategoryService;

    /**
     * 获取模型分类列表
     *
     * @return 分类列表
     */
    @ModelApiAnnotations.GetModelCategoriesApiDoc()
    @GetMapping("/categories")
    public ResponseEntity<RestResult> getModelCategories() {
        return executeWithTryCatch(() -> {
            List<ModelCategoryVo> categories = modelCategoryService.getModelCategories();
            return RestResult.ok("获取成功", categories);
        }, "获取模型分类失败，请稍后重试");
    }

    /**
     * 获取模型列表
     *
     * @param requestParams 查询参数
     * @return 模型列表
     */
    @ModelApiAnnotations.GetModelListApiDoc()
    @ApiParamValidate(bizParamClass = ModelListParam.class)
    @PostMapping("/list")
    public ResponseEntity<RestResult> getModelList(RequestParams<ModelListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取模型列表
            PageResult<ModelListVo> result = modelService.getModelList(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取模型列表失败，请稍后重试");
    }

    /**
     * 获取模型详情
     *
     * @param requestParams 查询参数
     * @return 模型详情
     */
    @ModelApiAnnotations.GetModelDetailApiDoc()
    @ApiParamValidate(bizParamClass = ModelDetailParam.class)
    @PostMapping("/detail")
    public ResponseEntity<RestResult> getModelDetail(RequestParams<ModelDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取模型详情
            ModelVo modelVo = modelService.getModelDetail(requestParams);
            checkNotNull(modelVo, "模型不存在或已下线");
            return RestResult.ok("获取成功", modelVo);
        }, "获取模型详情失败，请稍后重试");
    }

    /**
     * 根据编码获取模型
     *
     * @param requestParams 查询参数
     * @return 模型信息
     */
    @ModelApiAnnotations.GetModelByCodeApiDoc()
    @ApiParamValidate(bizParamClass = ModelByCodeParam.class)
    @PostMapping("/detail/code")
    public ResponseEntity<RestResult> getModelByCode(RequestParams<ModelByCodeParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取模型信息
            ModelVo modelVo = modelService.getModelByCode(requestParams);
            checkNotNull(modelVo, "模型不存在或已下线");
            return RestResult.ok("获取成功", modelVo);
        }, "获取模型信息失败，请稍后重试");
    }

    /**
     * 获取热门模型
     *
     * @return 热门模型列表
     */
    @ModelApiAnnotations.GetPopularModelsApiDoc()
    @GetMapping("/popular")
    public ResponseEntity<RestResult> getPopularModels() {
        return executeWithTryCatch(() -> {
            List<ModelListVo> popularModels = modelService.getPopularModels();
            return RestResult.ok("获取成功", popularModels);
        }, "获取热门模型失败，请稍后重试");
    }

    // ==================== 会员权限相关功能 ====================

    /**
     * 获取当前会员可访问的模型列表
     * 根据会员等级、角色、地区等权限控制
     *
     * @param requestParams 查询参数
     * @return 可访问的模型列表
     */
    @ModelApiAnnotations.GetAccessibleModelsApiDoc()
    @ApiParamValidate(bizParamClass = ModelListParam.class)
    @PostMapping("/accessible")
    public ResponseEntity<RestResult> getAccessibleModels(RequestParams<ModelListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取可访问的模型列表
            PageResult<ModelListVo> result = modelService.getAccessibleModels(requestParams);
            return RestResult.ok("获取成功", result);
        }, "获取可访问模型失败，请稍后重试");
    }

    /**
     * 验证当前会员是否有权限使用指定模型
     *
     * @param requestParams 查询参数
     * @return 权限验证结果
     */
    @ModelApiAnnotations.CheckModelAccessApiDoc()
    @ApiParamValidate(bizParamClass = ModelDetailParam.class)
    @PostMapping("/check-access")
    public ResponseEntity<RestResult> checkModelAccess(RequestParams<ModelDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务验证模型访问权限
            boolean hasAccess = modelService.checkModelAccess(requestParams);
            return RestResult.ok("验证成功", Map.of("hasAccess", hasAccess));
        }, "权限验证失败，请稍后重试");
    }

    // ==================== 会员个性化功能 ====================

    /**
     * 获取会员收藏的模型列表
     *
     * @return 收藏的模型列表
     */
    @ModelApiAnnotations.GetFavoriteModelsApiDoc()
    @GetMapping("/favorites")
    public ResponseEntity<RestResult> getFavoriteModels() {
        return executeWithTryCatch(() -> {
            List<ModelListVo> favoriteModels = modelService.getFavoriteModels();
            return RestResult.ok("获取成功", favoriteModels);
        }, "获取收藏模型失败，请稍后重试");
    }

    /**
     * 收藏/取消收藏模型
     *
     * @param requestParams 请求参数
     * @return 操作结果
     */
    @ModelApiAnnotations.ToggleFavoriteModelApiDoc()
    @ApiParamValidate(bizParamClass = ModelDetailParam.class)
    @PostMapping("/toggle-favorite")
    public ResponseEntity<RestResult> toggleFavoriteModel(RequestParams<ModelDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务切换收藏状态
            boolean isFavorited = modelService.toggleFavoriteModel(requestParams);
            String message = isFavorited ? "收藏成功" : "取消收藏成功";
            return RestResult.ok(message, Map.of("isFavorited", isFavorited));
        }, "操作失败，请稍后重试");
    }

    /**
     * 获取会员最近使用的模型
     *
     * @return 最近使用的模型列表
     */
    @ModelApiAnnotations.GetRecentModelsApiDoc()
    @GetMapping("/recent")
    public ResponseEntity<RestResult> getRecentModels() {
        return executeWithTryCatch(() -> {
            List<ModelListVo> recentModels = modelService.getRecentModels();
            return RestResult.ok("获取成功", recentModels);
        }, "获取最近使用模型失败，请稍后重试");
    }

    /**
     * 获取为当前会员推荐的模型
     * 基于使用习惯、会员等级等推荐
     *
     * @return 推荐模型列表
     */
    @ModelApiAnnotations.GetRecommendedModelsApiDoc()
    @GetMapping("/recommended")
    public ResponseEntity<RestResult> getRecommendedModels() {
        return executeWithTryCatch(() -> {
            List<ModelListVo> recommendedModels = modelService.getRecommendedModels();
            return RestResult.ok("获取成功", recommendedModels);
        }, "获取推荐模型失败，请稍后重试");
    }

    // ==================== 模型状态和监控功能 ====================

    /**
     * 检查模型服务可用性
     *
     * @param requestParams 查询参数
     * @return 模型状态信息
     */
    @ModelApiAnnotations.CheckModelStatusApiDoc()
    @ApiParamValidate(bizParamClass = ModelDetailParam.class)
    @PostMapping("/status")
    public ResponseEntity<RestResult> checkModelStatus(RequestParams<ModelDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务检查模型状态
            ModelStatusVo status = modelService.checkModelStatus(requestParams);
            return RestResult.ok("检查成功", status);
        }, "模型状态检查失败，请稍后重试");
    }

    /**
     * 获取模型性能指标
     *
     * @param requestParams 查询参数
     * @return 性能指标数据
     */
    @ModelApiAnnotations.GetModelMetricsApiDoc()
    @ApiParamValidate(bizParamClass = ModelDetailParam.class)
    @PostMapping("/metrics")
    public ResponseEntity<RestResult> getModelMetrics(RequestParams<ModelDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取模型性能指标
            ModelPerformanceMetricsVo metrics = modelService.getModelMetrics(requestParams);
            return RestResult.ok("获取成功", metrics);
        }, "获取模型指标失败，请稍后重试");
    }

    // ==================== 模型使用统计功能 ====================

    /**
     * 获取当前会员的模型使用统计
     *
     * @return 使用统计数据
     */
    @ModelApiAnnotations.GetMemberUsageStatsApiDoc()
    @GetMapping("/usage-stats")
    public ResponseEntity<RestResult> getMemberUsageStats() {
        return executeWithTryCatch(() -> {
            MemberUsageStatsVo stats = modelService.getMemberUsageStats();
            return RestResult.ok("获取成功", stats);
        }, "获取使用统计失败，请稍后重试");
    }

    /**
     * 获取指定模型的使用统计（当前会员）
     *
     * @param requestParams 查询参数
     * @return 模型使用统计
     */
    @ModelApiAnnotations.GetModelUsageStatsApiDoc()
    @ApiParamValidate(bizParamClass = ModelDetailParam.class)
    @PostMapping("/model-usage-stats")
    public ResponseEntity<RestResult> getModelUsageStats(RequestParams<ModelDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务获取模型使用统计
            MemberModelUsageStatsVo stats = modelService.getModelUsageStats(requestParams);
            return RestResult.ok("获取成功", stats);
        }, "获取模型使用统计失败，请稍后重试");
    }
}
